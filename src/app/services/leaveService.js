const { leave } = require('../../domain/entities');
const { convertDateToDDMMMYYYY } = require('../utils');

class LeaveService {
  constructor(container) {
    const {
      db,
      utils,
      entities,
      clientErrors,
      leaveRepository,
      fastify,
      requisitionApproverRepository,
      requisitionRepository,
      canvassApproverRepository,
      notificationService,
      constants,
      purchaseOrderApproverRepository,
      rsPaymentRequestApproverRepository,
    } = container;

    this.db = db;
    this.utils = utils;
    this.constants = constants;
    this.clientErrors = clientErrors;
    this.entities = entities;
    this.leaveRepository = leaveRepository;
    this.fastify = fastify;
    this.requisitionApproverRepository = requisitionApproverRepository;
    this.requisitionRepository = requisitionRepository;
    this.canvassApproverRepository = canvassApproverRepository;
    this.notificationService = notificationService;
    this.Sequelize = db.Sequelize;
    this.purchaseOrderApproverRepository = purchaseOrderApproverRepository;
    this.rsPaymentRequestApproverRepository =
      rsPaymentRequestApproverRepository;
  }

  //safety measure
  async getAllLeaves(request) {
    const { sortBy, filterBy, ...queries } = request.query;

    const leaves = await this.leaveRepository.findAll({
      ...queries,
    });

    return leaves;
  }

  async getAllLeavesByUserId(request) {
    const { userFromToken } = request;
    const { sortBy, ...queries } = request.query;
    const { leaveSortSchema } = this.entities.leave;
    const parsedSortBy = leaveSortSchema.parse(sortBy);
    const where = {
      userId: userFromToken.id,
    };

    const leaves = await this.leaveRepository.findAll({
      ...queries,
      order: parsedSortBy,
      where,
    });
    return leaves;
  }

  getDatesInRange = (startDate, endDate) => {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const dates = [];

    while (start <= end) {
      const formattedDate = start.toISOString().split('T')[0];
      dates.push(formattedDate);
      start.setDate(start.getDate() + 1);
    }

    return dates;
  };

  isDateInRange(date, startDate, endDate) {
    const targetDate = new Date(date);
    const rangeStart = new Date(startDate);
    const rangeEnd = new Date(endDate);
    return targetDate >= rangeStart && targetDate <= rangeEnd;
  }

  filterDatesInRange(dates, startDate, endDate) {
    return dates.filter((date) => this.isDateInRange(date, startDate, endDate));
  }

  parseDate(date) {
    var mdy = date.split('-');
    return new Date(mdy[0], mdy[1] - 1, mdy[2]);
  }

  checkTotalDays(startDate, endDate) {
    return Math.round((endDate - startDate) / (1000 * 60 * 60 * 24)) + 1;
  }

  async createLeave(request) {
    const { id, startDate, endDate } = request;
    this.fastify.log.info(`Creating Leave Case for User ID of ${id}`);

    const userLeaves = await this.leaveRepository.findAll({
      where: {
        userId: id,
      },
    });
    if (userLeaves?.data?.length) {
      let dateList = [];
      userLeaves.data.forEach((data) =>
        dateList.push(
          this.getDatesInRange(
            new Date(data.startDate),
            new Date(data.endDate),
          ),
        ),
      );

      let hasOverlappingLeaves = false;

      dateList.forEach((dates) => {
        const hasLeaves = this.filterDatesInRange(
          dates,
          new Date(startDate),
          new Date(endDate),
        );
        if (hasLeaves.length) {
          hasOverlappingLeaves = true;
          return;
        }
      });

      if (hasOverlappingLeaves) {
        throw this.clientErrors.BAD_REQUEST({
          message: `Selected leave dates should not overlap with an existing leave dates`,
        });
      }
    }

    const result = await this.leaveRepository.create({
      userId: id,
      startDate,
      endDate,
      totalDays: this.checkTotalDays(
        this.parseDate(startDate),
        this.parseDate(endDate),
      ),
    });
    this.fastify.log.info(`Created Leave Case for User ID of ${id}`);
    return result;
  }

  async getLeaveByPk(request) {
    this.fastify.log.info(
      `Retrieving information about Leave Case with ID of ${request}`,
    );

    const { CANVASS_STATUS } = this.constants.canvass;
    const { REQUISITION_STATUS } = this.constants.requisition;
    const { PO_STATUS } = this.constants.purchaseOrder;
    const { RS_PAYMENT_REQUEST_STATUS } = this.constants.rsPaymentRequest;

    const leave = await this.leaveRepository.getById(request, {
      include: [
        {
          model: this.db.userModel,
          attributes: ['id', 'firstName', 'lastName', 'username', 'email'],
          include: [
            {
              model: this.db.roleModel,
              attributes: ['id', 'name'],
              as: 'role',
            },
          ],
          as: 'user',
        },
        {
          model: this.db.userModel,
          attributes: ['id', 'firstName', 'lastName', 'username', 'email'],
          include: [
            {
              model: this.db.roleModel,
              attributes: ['id', 'name'],
              as: 'role',
            },
          ],
          as: 'rsAltUser',
        },
        {
          model: this.db.userModel,
          attributes: ['id', 'firstName', 'lastName', 'username', 'email'],
          include: [
            {
              model: this.db.roleModel,
              attributes: ['id', 'name'],
              as: 'role',
            },
          ],
          as: 'canvassAltUser',
        },
        {
          model: this.db.userModel,
          attributes: ['id', 'firstName', 'lastName', 'username', 'email'],
          include: [
            {
              model: this.db.roleModel,
              attributes: ['id', 'name'],
              as: 'role',
            },
          ],
          as: 'poAltUser',
        },
        {
          model: this.db.userModel,
          attributes: ['id', 'firstName', 'lastName', 'username', 'email'],
          include: [
            {
              model: this.db.roleModel,
              attributes: ['id', 'name'],
              as: 'role',
            },
          ],
          as: 'prAltUser',
        },
      ],
    });

    const { startDate, endDate, userId } = leave;

    if (!leave) {
      throw this.clientErrors.NOT_FOUND({
        message: `Leave not found`,
      });
    }

    const dates = (start, end) => {
      const dateList = [];
      for (
        const day = new Date(start);
        day <= new Date(end);
        day.setDate(day.getDate() + 1)
      ) {
        dateList.push(new Date(day));
      }
      return dateList;
    };
    const dateRange = dates(startDate, endDate);

    //for testing uncomment
    // const dateRange = dates('2024-10-16T00:00:00.000Z', endDate);

    //Affected Flows
    const requisitions =
      await this.requisitionApproverRepository.getAllRequisitionByUserId({
        where: {
          approverId: userId,
        },
        whereRequisition: {
          [this.Sequelize.Op.and]: [
            {
              status: {
                [this.Sequelize.Op.in]: [REQUISITION_STATUS.SUBMITTED],
              },
            },
          ],
        },
      });
    const canvasses =
      await this.canvassApproverRepository.getAllCanvassByUserId({
        where: {
          userId,
        },
        whereCanvass: {
          [this.Sequelize.Op.and]: [
            {
              status: { [this.Sequelize.Op.in]: [CANVASS_STATUS.FOR_APPROVAL] },
            },
          ],
        },
        whereRequisition: {
          [this.Sequelize.Op.and]: [],
        },
      });

    const purchaseOrders =
      await this.purchaseOrderApproverRepository.getAllPurchaseOrderByUserId({
        where: {
          userId,
        },
        whereRequisition: {
          [this.Sequelize.Op.and]: [],
        },
        whereCanvass: {
          [this.Sequelize.Op.and]: [
            {
              status: { [this.Sequelize.Op.in]: [CANVASS_STATUS.APPROVED] },
            },
          ],
        },
        wherePurchaseOrder: {
          [this.Sequelize.Op.and]: [
            {
              status: { [this.Sequelize.Op.in]: [PO_STATUS.FOR_PO_APPROVAL] },
            },
          ],
        },
      });

    const paymentRequests =
      await this.rsPaymentRequestApproverRepository.getAllRsPaymentRequestByUserId(
        {
          where: {
            userId,
          },
          whereRequisition: {
            [this.Sequelize.Op.and]: [],
          },
          whereCanvass: {
            [this.Sequelize.Op.and]: [
              {
                status: { [this.Sequelize.Op.in]: [CANVASS_STATUS.APPROVED] },
              },
            ],
          },
          wherePurchaseOrder: {
            [this.Sequelize.Op.and]: [
              {
                status: { [this.Sequelize.Op.in]: [PO_STATUS.APPROVED] },
              },
            ],
          },
          wherePaymentRequest: {
            [this.Sequelize.Op.and]: [
              {
                status: {
                  [this.Sequelize.Op.in]: [
                    RS_PAYMENT_REQUEST_STATUS.FOR_PR_APPROVAL,
                  ],
                },
              },
            ],
          },
        },
      );

    return {
      ...leave,
      requisitions,
      canvasses,
      purchaseOrders,
      paymentRequests,
    };
  }

  async getWorkflowByUserId(request) {
    this.fastify.log.info(
      `Retrieving information about workflow with user ID of ${request}`,
    );

    const { CANVASS_STATUS } = this.constants.canvass;
    const { REQUISITION_STATUS } = this.constants.requisition;
    const { PO_STATUS } = this.constants.purchaseOrder;
    const { RS_PAYMENT_REQUEST_STATUS } = this.constants.rsPaymentRequest;

    const leaves = await this.leaveRepository.findAll({
      where: {
        userId: request,
      },
      include: {
        model: this.db.userModel,
        attributes: ['id', 'firstName', 'lastName', 'username', 'email'],
        include: [
          {
            model: this.db.roleModel,
            attributes: ['id', 'name'],
            as: 'role',
          },
        ],
        as: 'user',
      },
    });

    if (!leaves) {
      throw this.clientErrors.NOT_FOUND({
        message: `Leave not found`,
      });
    }

    const ongoingLeave = leaves.data.find((date) => {
      const startDate = new Date(date.startDate);
      const endDate = new Date(date.endDate);
      const currDate = new Date(new Date().setHours(0, 0, 0, 0));

      return currDate >= startDate && currDate <= endDate;
    });
    const dates = (start, end) => {
      const dateList = [];
      for (
        const day = new Date(start);
        day <= new Date(end);
        day.setDate(day.getDate() + 1)
      ) {
        dateList.push(new Date(day));
      }
      return dateList;
    };

    const dateRange = dates(ongoingLeave?.startDate, ongoingLeave?.endDate);

    const requisitions =
      await this.requisitionApproverRepository.getAllRequisitionByUserId({
        whereRequisition: {
          [this.Sequelize.Op.and]: [
            {
              status: {
                [this.Sequelize.Op.in]: [REQUISITION_STATUS.SUBMITTED],
              },
            },
          ],
        },
      });

    const canvasses =
      await this.canvassApproverRepository.getAllCanvassByUserId({
        whereCanvass: {
          [this.Sequelize.Op.and]: [
            {
              status: { [this.Sequelize.Op.in]: [CANVASS_STATUS.FOR_APPROVAL] },
            },
          ],
        },
      });

    const purchaseOrders =
      await this.purchaseOrderApproverRepository.getAllPurchaseOrderByUserId({
        whereCanvass: {
          [this.Sequelize.Op.and]: [
            {
              status: { [this.Sequelize.Op.in]: [CANVASS_STATUS.APPROVED] },
            },
          ],
        },
        wherePurchaseOrder: {
          [this.Sequelize.Op.and]: [
            {
              status: { [this.Sequelize.Op.in]: [PO_STATUS.FOR_PO_APPROVAL] },
            },
          ],
        },
      });

    const paymentRequests =
      await this.rsPaymentRequestApproverRepository.getAllRsPaymentRequestByUserId(
        {
          whereCanvass: {
            [this.Sequelize.Op.and]: [
              {
                status: { [this.Sequelize.Op.in]: [CANVASS_STATUS.APPROVED] },
              },
            ],
          },
          wherePurchaseOrder: {
            [this.Sequelize.Op.and]: [
              {
                status: { [this.Sequelize.Op.in]: [PO_STATUS.APPROVED] },
              },
            ],
          },
          wherePaymentRequest: {
            [this.Sequelize.Op.and]: [
              {
                status: {
                  [this.Sequelize.Op.in]: [
                    RS_PAYMENT_REQUEST_STATUS.FOR_PR_APPROVAL,
                  ],
                },
              },
            ],
          },
        },
      );

    return {
      requisitions,
      canvasses,
      purchaseOrders,
      paymentRequests,
      ongoingLeave,
    };
  }

  async updateLeave(request) {
    const { id, body, userFromToken } = request;
    this.fastify.log.info(`Updating Leave Case of ID ${id}`);

    const { canvasses, requisitions, purchaseOrders, paymentRequests } =
      await this.getLeaveByPk(id);

    let altApproverIDs = [];

    const getId = (arr) => {
      let newArr = [];
      let altApproverId = null;

      arr.forEach((element) => {
        if (element?.altApproverId) altApproverId = element?.altApproverId;
        newArr.push(element.id);
      });

      return { newArr, altApproverId };
    };

    if (new Date(body.startDate) > new Date().toJSON().slice(0, 10)) {
      throw this.clientErrors.BAD_REQUEST({
        message: `Start Date is today cannot be Edited`,
      });
    }

    const userLeaves = await this.leaveRepository.findAll({
      where: {
        userId: userFromToken.id,
        id: { [this.Sequelize.Op.not]: id },
      },
    });

    if (userLeaves?.data?.length) {
      let dateList = [];
      userLeaves.data.forEach((data) =>
        dateList.push(
          this.getDatesInRange(
            new Date(data.startDate),
            new Date(data.endDate),
          ),
        ),
      );

      let hasOverlappingLeaves = false;

      dateList.forEach((dates) => {
        const hasLeaves = this.filterDatesInRange(
          dates,
          new Date(body.startDate),
          new Date(body.endDate),
        );
        if (hasLeaves.length) {
          hasOverlappingLeaves = true;
          return;
        }
      });

      if (hasOverlappingLeaves) {
        throw this.clientErrors.BAD_REQUEST({
          message: `Selected leave dates should not overlap with an existing leave dates`,
        });
      }
    }
    const result = await this.leaveRepository.update(
      { id },
      {
        ...body,
        totalDays: this.checkTotalDays(
          this.parseDate(body.startDate),
          this.parseDate(body.endDate),
        ),
      },
    );

    const leaveData = await this.leaveRepository.getById(id)

    if (requisitions && leaveData.rsUserId) {
       altApproverIDs.push(leaveData.rsUserId);
    }
    if (canvasses && leaveData.canvassUserId) {
      altApproverIDs.push(leaveData.canvassUserId);
    }
    if (purchaseOrders  && leaveData.poUserId) {
      altApproverIDs.push(leaveData.poUserId);
    }
    
    if (purchaseOrders  && leaveData.prUserId) {
      altApproverIDs.push(leaveData.prUserId);
    }

    await this.notificationService.sendNotification({
      title: `Updated Leave Dates`,
      message: `${userFromToken.firstName} ${userFromToken.lastName} has updated their Leave Dates to ${new Date(result[1][0].dataValues.startDate).toJSON().slice(0, 10)} - ${new Date(result[1][0].dataValues.endDate).toJSON().slice(0, 10)}. You will be Approving on their behalf by the given Dates.`,
      recipientUserIds: altApproverIDs,
      type: `Alt Approver Assignment`,
      senderId: userFromToken.id,
    });

    this.fastify.log.info(`Updated Leave Case of ID ${id}`);
    return result;
  }

  async deleteLeave(request) {
    const { id, userFromToken } = request;
    this.fastify.log.info(`Deleting Leave Case of ID ${id}`);
    const {
      canvasses,
      requisitions,
      purchaseOrders,
      paymentRequests,
      startDate,
      endDate,
    } = await this.getLeaveByPk(id);
    let altApproverIDs = [];

    const getId = (arr) => {
      let newArr = [];
      let altApproverId = null;

      arr.forEach((element) => {
        if (element?.altApproverId) altApproverId = element?.altApproverId;
        newArr.push(element.id);
      });

      return { newArr, altApproverId };
    };

    //Get alternate approvers from requisitions, canvasses, purchaseOrders, and paymentRequests
    const leaveData = await this.leaveRepository.getById(id)

    if (requisitions && leaveData.rsUserId) {
       altApproverIDs.push(leaveData.rsUserId);
    }
    if (canvasses && leaveData.canvassUserId) {
      altApproverIDs.push(leaveData.canvassUserId);
    }
    if (purchaseOrders  && leaveData.poUserId) {
      altApproverIDs.push(leaveData.poUserId);
    }
    
    if (purchaseOrders  && leaveData.prUserId) {
      altApproverIDs.push(leaveData.prUserId);
    }


    const result = await this.leaveRepository.destroy(
      { id },
      { paranoid: true },
    );

    await this.notificationService.sendNotification({
      title: `Cancelled Alt Approver Access`,
      message: `${userFromToken.firstName} ${userFromToken.lastName}  has canceled their Leave Dates to ${new Date(startDate).toJSON().slice(0, 10)} - ${new Date(endDate).toJSON().slice(0, 10)}. You will no longer be Approving on their behalf by the given Dates.`,
      recipientUserIds: altApproverIDs,
      type: `Alt Approver Assignment`,
      senderId: userFromToken.id,
    });
    this.fastify.log.info(`Deleted Leave Case of ID ${id}`);
    return result;
  }

  async assignApprover(request) {
    const { userFromToken } = request;

    const phTimeToday = new Date(new Date().setUTCHours(0, 0, 0, 0));

    const leaves = await this.leaveRepository.findAll({
      where: {
        endDate: {
          [this.Sequelize.Op.gte]: phTimeToday,
        },
        startDate: {
          [this.Sequelize.Op.lte]: phTimeToday,
        },
      },

      include: {
        model: this.db.userModel,
        attributes: ['id', 'firstName', 'lastName', 'username', 'email'],
        include: [
          {
            model: this.db.roleModel,
            attributes: ['id', 'name'],
            as: 'role',
          },
        ],
        as: 'user',
      },
    });

    if (!leaves) {
      throw this.clientErrors.NOT_FOUND({
        message: `Leave not found`,
      });
    }

    const ongoingLeaves = leaves.data.filter((date) => {
      const startDate = new Date(date.startDate);
      const endDate = new Date(date.endDate);

      return phTimeToday >= startDate && phTimeToday <= endDate;
    });

    if (!ongoingLeaves.length) {
      throw this.clientErrors.NOT_FOUND({
        message: `There is no ongoing leave for the user`,
      });
    }

    let data = [];
    await Promise.all(
      ongoingLeaves.map(async (leave) => {
        const rsResult = await this.requisitionApproverRepository.update(
          {
            approverId: leave.userId,
            status: 'pending',
          },
          {
            isAltApprover: true,
            altApproverId: leave?.rsUserId,
          },
        );
        data.push({ rsResult });

        const csResult = await this.canvassApproverRepository.update(
          {
            userId: leave.userId,
            status: 'pending',
          },
          {
            isAltApprover: true,
            altApproverId: leave?.rsUserId,
          },
        );
        data.push({ csResult });

        const poResult = await this.purchaseOrderApproverRepository.update(
          {
            userId: leave.userId,
            status: 'pending',
          },
          {
            isAltApprover: true,
            altApproverId: leave?.rsUserId,
          },
        );
        data.push({ poResult });

        const prResult = await this.rsPaymentRequestApproverRepository.update(
          {
            userId: leave.userId,
            status: 'pending',
          },
          {
            isAltApprover: true,
            altApproverId: leave?.rsUserId,
          },
        );
        data.push({ prResult });
      }),
    );

    return data;
  }

  async addAltApprover(request) {
    const {
      id,
      userId,
      toChange,
      userFromToken,
      withNotification = true,
      startDate,
      endDate,
      leaveId,
      leave,
    } = request;
    let data = [];
    await Promise.all(
      id.map(async (id) => {
        if (toChange === 'requisition') {
          const approver = await this.requisitionApproverRepository.getById(id);

          if (!approver) {
            throw this.clientErrors.NOT_FOUND({
              message: `Requisition Approver not found with ID of ${id}`,
            });
          }

          if (leaveId !== null && leaveId !== undefined && leaveId !== '') {
            const leaveResult = await this.leaveRepository.update(
              { id: leaveId },
              {
                rsUserId: userId,
              },
            );

            if (!leaveResult) {
              throw this.clientErrors.NOT_FOUND({
                message: `Cannot add alternative user in leave. Workflow: requisition`,
              });
            }
          }

          const result = await this.requisitionApproverRepository.update(
            {
              id,
            },
            {
              isAltApprover: true,
              altApproverId: userId,
            },
            { plain: true },
          );
          data.push({ result, previousValue: approver });
        }

        if (toChange === 'canvass') {
          const approver = await this.canvassApproverRepository.getById(id);

          if (!approver) {
            throw this.clientErrors.NOT_FOUND({
              message: `Canvass Approver not found with ID of ${id}`,
            });
          }

          if (leaveId !== null && leaveId !== undefined && leaveId !== '') {
            const leaveResult = await this.leaveRepository.update(
              { id: leaveId },
              {
                canvassUserId: userId,
              },
            );

            if (!leaveResult) {
              throw this.clientErrors.NOT_FOUND({
                message: `Cannot add alternative user in leave. Workflow: canvass`,
              });
            }
          }

          const result = await this.canvassApproverRepository.update(
            {
              id,
            },
            {
              altApproverId: userId,
            },
          );

          this.fastify.log.info(
            `Notifying Alternate Approver with ID ${userId}`,
          );

          data.push({ result, previousValue: approver });
        }

        if (toChange === 'po') {
          const approver =
            await this.purchaseOrderApproverRepository.getById(id);

          if (!approver) {
            throw this.clientErrors.NOT_FOUND({
              message: `Purchase Approver not found with ID of ${id}`,
            });
          }

          if (leaveId !== null && leaveId !== undefined && leaveId !== '') {
            const leaveResult = await this.leaveRepository.update(
              { id: leaveId },
              {
                poUserId: userId,
              },
            );

            if (!leaveResult) {
              throw this.clientErrors.NOT_FOUND({
                message: `Cannot add alternative user in leave. Workflow: canvass`,
              });
            }
          }

          const result = await this.purchaseOrderApproverRepository.update(
            {
              id,
            },
            {
              altApproverId: userId,
            },
          );

          this.fastify.log.info(
            `Notifying Alternate Approver with ID ${userId}`,
          );

          data.push({ result, previousValue: approver });
        }

        if (toChange === 'pr') {
          const approver =
            await this.rsPaymentRequestApproverRepository.getById(id);

          if (!approver) {
            throw this.clientErrors.NOT_FOUND({
              message: `Rs Payment Request Approver not found with ID of ${id}`,
            });
          }

          if (leaveId !== null && leaveId !== undefined && leaveId !== '') {
            const leaveResult = await this.leaveRepository.update(
              { id: leaveId },
              {
                prUserId: userId,
              },
            );

            if (!leaveResult) {
              throw this.clientErrors.NOT_FOUND({
                message: `Cannot add alternative user in leave. Workflow: canvass`,
              });
            }
          }

          const result = await this.rsPaymentRequestApproverRepository.update(
            {
              id,
            },
            {
              altApproverId: userId,
            },
          );

          this.fastify.log.info(
            `Notifying Alternate Approver with ID ${userId}`,
          );

          data.push({ result, previousValue: approver });
        }
      }),
    );

    const notifyUsersOnNewlyAddedAltApprover = async(workflowUserId, workflowName) => {
      if(workflowUserId) {
        await this.notificationService.sendNotification({
          title: `Alt Approver Assignment`,
          message: `You are now assigned as an Approver of ${workflowName}. And is expected to Approve any incoming Requests starting today. Go to Dashboard and click For My Approval/Assigned Tab to start Approving`,
          recipientUserIds: [userId],
          type: `Alt Approver Assignment`,
          senderId: userFromToken.id,
        });
      }
      else {
        await this.notificationService.sendNotification({
          title: `Alt Approver Assignment`,
          message: `${userFromToken.firstName} ${userFromToken.lastName} has assigned you as their Alt Approver during their Leave Dates, Start Date: ${convertDateToDDMMMYYYY(startDate)} End Date: ${convertDateToDDMMMYYYY(endDate)}. You will be Approving on their behalf by the given Dates.`,
          recipientUserIds: [userId],
          type: `Alt Approver Assignment`,
          senderId: userFromToken.id,
        });
      }
      
    }


    if (withNotification) {
      

      if (toChange === 'requisition') {
        notifyUsersOnNewlyAddedAltApprover(leave?.rsUserId, 'Requisition slip');
        await this.notificationService.sendNotification({
          title: `Updated Alt Approver Assignment`,
          message: `${userFromToken.firstName} ${userFromToken.lastName} has updated the Alt Approver for their Leave Dates.`,
          recipientUserIds: [leave?.rsUserId],
          type: `Alt Approver Assignment`,
          senderId: userFromToken.id,
        });
      } else if (toChange === 'canvass') {
        notifyUsersOnNewlyAddedAltApprover(leave?.canvassUserId, 'Canvass');
        await this.notificationService.sendNotification({
          title: `Updated Alt Approver Assignment`,
          message: `${userFromToken.firstName} ${userFromToken.lastName} has updated the Alt Approver for their Leave Dates.`,
          recipientUserIds: [leave?.canvassUserId],
          type: `Alt Approver Assignment`,
          senderId: userFromToken.id,
        });
      } else if (toChange === 'po') {
        notifyUsersOnNewlyAddedAltApprover(leave?.poUserId, 'Purchase order');
        await this.notificationService.sendNotification({
          title: `Updated Alt Approver Assignment`,
          message: `${userFromToken.firstName} ${userFromToken.lastName} has updated the Alt Approver for their Leave Dates.`,
          recipientUserIds: [leave?.poUserId],
          type: `Alt Approver Assignment`,
          senderId: userFromToken.id,
        });
      } else if (toChange === 'pr') {
        notifyUsersOnNewlyAddedAltApprover(leave?.prUserId, 'Payment request');
        await this.notificationService.sendNotification({
          title: `Updated Alt Approver Assignment`,
          message: `${userFromToken.firstName} ${userFromToken.lastName} has updated the Alt Approver for their Leave Dates.`,
          recipientUserIds: [leave?.prUserId],
          type: `Alt Approver Assignment`,
          senderId: userFromToken.id,
        });
      }
    }

    return data;
  }

  async getUserLeavesBetweenDateRequired(request) {
    const { id: requisitionId } = request;

    const today = new Date().toJSON().slice(0, 10);

    const result = await this.requisitionApproverRepository.findAll({
      where: { requisitionId },
      include: [
        {
          model: this.db.userModel,
          attributes: [
            'id',
            'username',
            [
              this.Sequelize.fn(
                'CONCAT',
                this.Sequelize.col('first_name'),
                ' ',
                this.Sequelize.col('last_name'),
              ),
              'fullName',
            ],
          ],
          include: [
            {
              model: this.db.leaveModel,
              attributes: ['id', 'startDate', 'endDate'],
              as: 'userLeaves',
              where: {
                [this.Sequelize.Op.and]: [
                  {
                    startDate: {
                      [this.Sequelize.Op.lte]: today,
                    },
                  },
                  {
                    endDate: {
                      [this.Sequelize.Op.gte]: today,
                    },
                  },
                ],
              },
            },
          ],
          as: 'approver',
        },
      ],
      order: [
        ['level', 'asc'],
        ['id', 'asc'],
      ],
    });
    if (result.data.length === 0) {
      return [];
    }

    return result.data[0].approver;
  }
}

module.exports = LeaveService;
